# AWS Configuration for ICU Dataset Application - EC2 Production Environment
# This file should be uploaded to your EC2 instance as .env.production

# Environment
NODE_ENV=production

# Frontend AWS Configuration (Cognito Identity Pool)
REACT_APP_AWS_IDENTITY_POOL_ID=ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd
REACT_APP_AWS_REGION=ap-southeast-2
REACT_APP_S3_BUCKET=icudatasetphrasesfortesting

# Backend AWS Configuration (IAM User Credentials)
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=WOFqaWc9pLOHmhubPAtMpFGAKdYffw9CJrkk8+Ik
AWS_REGION=ap-southeast-2
AWS_S3_BUCKET=icudatasetphrasesfortesting

# Server Configuration
PORT=5000

# Backend URL for API requests (UPDATE WITH YOUR EC2 PUBLIC IP)
# Replace YOUR_EC2_PUBLIC_IP with your actual EC2 instance public IP
REACT_APP_BACKEND_URL=http://YOUR_EC2_PUBLIC_IP:5000

# CORS allowed origins for backend server
# Includes localhost for testing and production domains
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002,http://localhost:3003,http://localhost:3004,http://localhost:3005,https://icuphrasecollection.com,https://*.netlify.app,https://main--icuphrasecollection.netlify.app

# Application Configuration
REACT_APP_NAME=ICU Dataset Application
REACT_APP_VERSION=1.0.0
REACT_APP_DEBUG=false

# SageMaker Configuration (if needed)
REACT_APP_SAGEMAKER_ENDPOINT=your-sagemaker-endpoint-here

# Additional Production Settings
MAX_FILE_SIZE=50MB
UPLOAD_TIMEOUT=30000
CONNECTION_TIMEOUT=10000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=/home/<USER>/logs/application.log

# Security Settings
TRUST_PROXY=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
